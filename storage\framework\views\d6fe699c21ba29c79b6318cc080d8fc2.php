<?php $__env->startSection('title', 'BD | Event Detail'); ?>

<?php $__env->startSection('content'); ?>
<?php echo $__env->make('bd.components.header', ['user' => $user ?? null], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<style>
    .expect-section .container {
        width: 60%;
    }

    /* Event Documents Styles */
    .document-card {
        background: #fff;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 15px;
        text-align: center;
        transition: all 0.3s ease;
        position: relative;
        height: 120px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .document-card:hover {
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        transform: translateY(-2px);
    }

    .document-icon {
        font-size: 2.5rem;
        margin-bottom: 8px;
    }

    .document-info {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .document-name {
        font-size: 0.85rem;
        font-weight: 600;
        margin-bottom: 2px;
        color: #333;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        max-width: 100%;
    }

    .document-type {
        font-size: 0.75rem;
        color: #666;
        text-transform: uppercase;
        font-weight: 500;
    }

    .document-download-btn {
        position: absolute;
        top: 8px;
        right: 8px;
        background: #f26925;
        color: white;
        border: none;
        border-radius: 50%;
        width: 28px;
        height: 28px;
        display: flex;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        font-size: 0.8rem;
        transition: all 0.3s ease;
    }

    .document-download-btn:hover {
        background: #d55a1f;
        color: white;
        text-decoration: none;
        transform: scale(1.1);
    }

    @media (max-width: 768px) {
        .document-card {
            height: 100px;
            padding: 10px;
        }

        .document-icon {
            font-size: 2rem;
            margin-bottom: 5px;
        }

        .document-name {
            font-size: 0.8rem;
        }

        .document-type {
            font-size: 0.7rem;
        }

        .document-download-btn {
            width: 24px;
            height: 24px;
            font-size: 0.7rem;
        }
    }
</style>

<!-- Welcome Section -->
<div class="container px-3 py-3">

<div class="row mb-4">
        <div class="col-12">
            <p class="welcome-text-mobile mb-0">Welcome to your BD events dashboard</p>
        </div>
    </div>


    <!-- Event Detail Card -->
    <div class="event-detail-card mb-4">
        <div class="event-image-container">
            <img src="<?php echo e($event->image ?? asset('assets/bd/images/event.png')); ?>" alt="Event Image" class="event-image">
            <?php if($event->is_registered ?? true): ?>
                <span class="registered-badge">Registered</span>
            <?php endif; ?>
        </div>
        <div class="event-content">

            <div class="d-flex justify-content-between align-items-center">
                <h5 class="event-title"><?php echo e($event->title ?? 'APAC Hospital at Home Symposium'); ?></h5>
            </div>
            <div class="event-details mb-3">
                <?php if(!empty($event->event_dates)): ?>
                    <?php $__currentLoopData = $event->event_dates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $eventDate): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="mb-2">
                            <small class="text-muted"><?php echo e($eventDate->formatted_date); ?></small><br>
                            <?php if(!empty($eventDate->location_url)): ?>
                                <small class="text-muted">
                                    <a href="<?php echo e($eventDate->location_url); ?>" target="_blank" style="font-style: italic;" class="" style="cursor: pointer;">
                                        <?php echo e($eventDate->location); ?>

                                        <i class="fas fa-external-link-alt ms-1" style="font-size: 0.8em;"></i>
                                    </a>
                                </small>
                            <?php else: ?>
                                <small class="text-muted" style="font-style: italic;"><?php echo e($eventDate->location); ?></small>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php else: ?>
                    <small class="text-muted">TODAY | 12:00PM-04:00PM</small><br>
                    <small class="text-muted">Location TBA</small>
                <?php endif; ?>
            </div>
            <p class="event-description">
                <?php echo e($event->description ?? 'Explore cutting-edge homecare solutions for patient management, including telemedicine, remote monitoring, and personalized care plans.'); ?>

            </p>
            <div class="d-flex justify-content-between align-items-center">
                <button class="btn contact-btn w-100 mb-3" data-bs-toggle="modal" data-bs-target="#contactModal">Contact</button>
            </div>
        </div>
    </div>

    <!-- Speakers Panel -->
    <div class="speakers-section mb-4">
        <h6 class="section-title">Speakers/Panel</h6>
        <div class="row g-3">
            <?php $__empty_1 = true; $__currentLoopData = $event->speakers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $speaker): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="col-12 col-md-3">
                    <div class="speaker-card">
                        <div class="speaker-image-placeholder"
                             style="background-image: url('<?php echo e($speaker->image); ?>'); background-size: cover; background-position: center;">
                        </div>
                        <h6 class="speaker-name"><?php echo e($speaker->name); ?></h6>
                        <button class="btn speaker-read-btn" onclick="window.location.href='<?php echo e(route('bd.speaker-detail', $speaker->id)); ?>'">
                            Read more
                        </button>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="col-12 text-center py-4">
                    <p class="text-muted">No speakers announced yet for this event.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- What to expect -->
<div class="expect-section mb-4">
    <div class="container px-3 py-3">
        <h6 class="section-title">What to expect from this event?</h6>
        <?php if($event->expectations ?? null): ?>
            <?php $__currentLoopData = $event->expectations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $expectation): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <p class="expect-text"><?php echo e($expectation); ?></p>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php else: ?>
            <p class="expect-text">
                Lorem ipsum dolor sit amet consectetur. Lectus est tempus sed sed venenatis amet.
            </p>
            <p class="expect-text">
                Purus mattis morbi sed sed amet facilisi nam purus quam. Facilisis ultrices libero commodo sed aenean fusce lectus.
            </p>
            <p class="expect-text">
                Viverra urna tincidunt consequat quis enim faucibus cras vitae odio. Sit adipiscing molestie amet auctor gravida aliquet.
            </p>
            <p class="expect-text">
                Urna non sapien lectus mauris feugiat in pharetra neque arcu. Nisl leo convallis cursus urna mauris mauris nunc urna.
            </p>
        <?php endif; ?>
    </div>
</div>

<div class="container px-3 py-3">
    <!-- Agenda Section -->
    <div class="agenda-section mb-4">
        <h6 class="section-title">Agenda</h6>

        <?php $__empty_1 = true; $__currentLoopData = $event->agenda ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $agendaItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="agenda-item">
                <div class="agenda-time"><?php echo e($agendaItem->time); ?></div>
                <div class="agenda-content">
                    <h6 class="agenda-topic"><?php echo e($agendaItem->topic); ?></h6>
                    <p class="agenda-speaker"><?php echo e($agendaItem->speaker); ?> - <?php echo e($agendaItem->moderator); ?></p>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <p> - </p>
        <?php endif; ?>
    </div>

    <!-- Event Documents Section -->
    <div class="event-documents-section mb-4">
        <h6 class="section-title">Event Documents</h6>

        <?php if($event->entire_event_documents && $event->entire_event_documents->count() > 0): ?>
            <div class="row g-3">
                <?php $__currentLoopData = $event->entire_event_documents; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $document): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="col-6 col-md-4 col-lg-3">
                        <div class="document-card">
                            <div class="document-icon">
                                <?php if($document->document_type == 'PDF'): ?>
                                    <i class="fas fa-file-pdf text-danger"></i>
                                <?php elseif($document->document_type == 'WORD'): ?>
                                    <i class="fas fa-file-word text-primary"></i>
                                <?php elseif($document->document_type == 'PPT'): ?>
                                    <i class="fas fa-file-powerpoint text-warning"></i>
                                <?php else: ?>
                                    <i class="fas fa-file text-secondary"></i>
                                <?php endif; ?>
                            </div>
                            <div class="document-info">
                                <h6 class="document-name" title="<?php echo e($document->document_name); ?>">
                                    <?php echo e(pathinfo($document->document_name, PATHINFO_FILENAME)); ?>

                                </h6>
                                <small class="document-type"><?php echo e($document->document_type); ?></small>
                            </div>
                            <a href="<?php echo e(asset(env('STORAGE_PATH') . '/event_documents/' . $document->document_name)); ?>"
                               target="_blank" class="document-download-btn">
                                <i class="bi bi-download"></i>
                            </a>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        <?php else: ?>
            <div class="text-center py-4">
                <p class="text-muted">No documents available for this event yet.</p>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Contact Modal -->
<div class="modal fade" id="contactModal" tabindex="-1" aria-labelledby="contactModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="contactModalLabel">Contact Event Organizer</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="contactForm">
                    <?php echo csrf_field(); ?>
                    <input type="hidden" id="eventId" value="<?php echo e($event->id ?? ''); ?>">
                    <div class="mb-3">
                        <label for="senderName" class="form-label">Your Name</label>
                        <input type="text" class="form-control" id="senderName" value="<?php echo e($user->name ?? ''); ?>" required>
                    </div>
                    <div class="mb-3">
                        <label for="senderEmail" class="form-label">Your Email</label>
                        <input type="email" class="form-control" id="senderEmail" value="<?php echo e($user->email ?? ''); ?>" required>
                    </div>
                    <div class="mb-3">
                        <label for="senderEmail" class="form-label">To</label>
                        <input type="email" class="form-control" id="senderEmail" disabled value="<?php echo e($event->contact_email ?? ''); ?>" required>
                    </div>
                    <div class="mb-3">
                        <label for="messageSubject" class="form-label">Subject</label>
                        <input type="text" class="form-control" id="messageSubject" value="Inquiry about <?php echo e($event->title ?? 'Event'); ?>" required>
                    </div>
                    <div class="mb-3">
                        <label for="messageContent" class="form-label">Message</label>
                        <textarea class="form-control" id="messageContent" rows="4" placeholder="Please enter your message..." required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="sendMessageBtn">Send Message</button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Send message handler
    const sendMessageBtn = document.getElementById('sendMessageBtn');
    if (sendMessageBtn) {
        sendMessageBtn.addEventListener('click', function() {
            const eventId = document.getElementById('eventId').value;
            const senderName = document.getElementById('senderName').value;
            const senderEmail = document.getElementById('senderEmail').value;
            const messageSubject = document.getElementById('messageSubject').value;
            const messageContent = document.getElementById('messageContent').value;

            // Validation
            if (!senderName || !senderEmail || !messageSubject || !messageContent) {
                BDUtils.showToast('Please fill in all fields', 'error');
                return;
            }

            // Show loading state
            sendMessageBtn.textContent = 'Sending...';
            sendMessageBtn.disabled = true;

            // Send message
            fetch('/event/contact', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    event_id: eventId,
                    sender_name: senderName,
                    sender_email: senderEmail,
                    subject: messageSubject,
                    message: messageContent
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    BDUtils.showToast('Message sent successfully!', 'success');
                    // Close modal and reset form
                    const modal = bootstrap.Modal.getInstance(document.getElementById('contactModal'));
                    modal.hide();
                    document.getElementById('contactForm').reset();
                } else {
                    BDUtils.showToast(data.message || 'Failed to send message', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                BDUtils.showToast('Failed to send message', 'error');
            })
            .finally(() => {
                sendMessageBtn.textContent = 'Send Message';
                sendMessageBtn.disabled = false;
            });
        });
    }
});
</script>
<script>
// expect-section container width
const refSection = document.querySelector(".speakers-section");
const targetContainer = document.querySelector(".expect-section .container");

if (refSection && targetContainer) {
    function setWidth() {
        targetContainer.style.width = refSection.offsetWidth + "px";
        targetContainer.style.margin = "0 auto"; // ortalamak için
    }

    // Başlangıçta uygula
    setWidth();

    // Sayfa yeniden boyutlanınca tekrar uygula
    window.addEventListener("resize", setWidth);
}

</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.bd-app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\bd-qr-badge\resources\views/bd/event-detail.blade.php ENDPATH**/ ?>