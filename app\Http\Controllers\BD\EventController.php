<?php

namespace App\Http\Controllers\BD;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Mail;
use App\Models\Event;
use App\Models\Register;
use Carbon\Carbon;
use App\Models\EventDate;
use App\Models\EventModeratorSpearker;

class EventController extends Controller
{
    /**
     * Show event detail (generic)
     */
    public function detail()
    {
        $user = Session::get('bd_user');

        // Mock event data
        $event = (object) [
            'id' => 1,
            'title' => 'APAC Hospital at Home Symposium',
            'formatted_date' => 'TODAY | 12:00PM-04:00PM',
            'location' => 'Auditorium, National University...',
            'description' => 'Explore cutting-edge homecare solutions for patient management, including telemedicine, remote monitoring, and personalized care plans.',
            'image' => asset('assets/bd/images/event.png'),
            'is_registered' => true,
            'speakers' => [
                (object) [
                    'id' => 1,
                    'name' => 'Dr. <PERSON>',
                    'title' => 'Chief Medical Officer',
                    'image' => asset('assets/bd/images/user.png')
                ],
                (object) [
                    'id' => 2,
                    'name' => 'Dr. <PERSON>',
                    'title' => 'Director of Innovation',
                    'image' => asset('assets/bd/images/user.png')
                ],
                (object) [
                    'id' => 3,
                    'name' => 'Dr. Michael Rodriguez',
                    'title' => 'Head of Research',
                    'image' => asset('assets/bd/images/user.png')
                ],
                (object) [
                    'id' => 4,
                    'name' => 'Dr. Emily Watson',
                    'title' => 'Clinical Specialist',
                    'image' => asset('assets/bd/images/user.png')
                ]
            ],
            'moderators' => [
                (object) [
                    'id' => 1,
                    'name' => 'Dr. İlker Devrim',
                    'title' => 'Chief Medical Officer',
                    'image' => asset('assets/bd/images/user.png')
                ]
            ],
            'expectations' => [
                'Lorem ipsum dolor sit amet consectetur. Lectus est tempus sed sed venenatis amet.',
                'Purus mattis morbi sed sed amet facilisi nam purus quam. Facilisis ultrices libero commodo sed aenean fusce lectus.',
                'Viverra urna tincidunt consequat quis enim faucibus cras vitae odio. Sit adipiscing molestie amet auctor gravida aliquet.',
                'Urna non sapien lectus mauris feugiat in pharetra neque arcu. Nisl leo convallis cursus urna mauris mauris nunc urna.'
            ],
            'agenda' => [
                (object) [
                    'time' => '09:00 - 09:30',
                    'topic' => 'Registration and Welcome Coffee',
                    'speaker' => 'Event Team',
                    'moderator' => 'Moderator TBA'
                ],
                (object) [
                    'time' => '09:30 - 10:30',
                    'topic' => 'Future of Hospital at Home: Trends and Innovations',
                    'speaker' => 'Dr. İlker Devrim',
                    'moderator' => 'Moderator TBA'
                ],
                (object) [
                    'time' => '10:30 - 11:30',
                    'topic' => 'Technology Integration in Home Healthcare',
                    'speaker' => 'Dr. Sarah Chen',
                    'moderator' => 'Moderator TBA'
                ],
                (object) [
                    'time' => '11:30 - 12:30',
                    'topic' => 'Patient Safety and Quality Assurance',
                    'speaker' => 'Dr. Michael Rodriguez',
                    'moderator' => 'Moderator TBA'
                ]
            ]
        ];

        return view('bd.event-detail', compact('user', 'event'));
    }

    /**
     * Show specific event
     */
    public function show($id)
    {
        $user = Session::get('bd_user');

        if (!$user) {
            return redirect()->route('bd.login');
        }

        // Fetch event from database with relationships
        $eventData = Event::with(['event_dates', 'moderator_speaker', 'sessions', 'documents'])
            ->where('id', $id)
            ->where('status', 1)
            ->first();

        if (!$eventData) {
            return redirect()->route('bd.dashboard')->with('error', 'Event not found');
        }

        // Check if user is registered for this event
        $isRegistered = Register::where('email', $user->email)
            ->where('event_id', $id)
            ->exists();

        // Format event data
        $event = $this->formatEventDetailData($eventData, $isRegistered);

        return view('bd.event-detail', compact('user', 'event'));
    }

    /**
     * Format event data for detail view
     */
    private function formatEventDetailData($eventData, $isRegistered = false)
    {
        // Format all event dates
        $eventDates = [];

        if ($eventData->event_dates && $eventData->event_dates->count() > 0) {
            foreach ($eventData->event_dates as $eventDate) {
                $startDate = Carbon::parse($eventDate->event_date);
                $startTime = $eventDate->event_start_time ?? '09:00 AM';
                $endTime = $eventDate->event_end_time ?? '05:00 PM';

                $dateText = $startDate->format('M d, Y');
                $timeText = $this->formatTime($startTime) . '-' . $this->formatTime($endTime);

                $eventDates[] = (object) [
                    'formatted_date' => $dateText . ' | ' . $timeText,
                    'location' => $this->buildLocationString($eventDate),
                    'location_url' => $eventDate->event_location_url
                ];
            }
        } else {
            // Fallback to event table dates if no event_dates found
            $startDate = $eventData->event_date ? Carbon::parse($eventData->event_date) : Carbon::now();
            $startTime = $eventData->event_start_time ?? '09:00 AM';
            $endTime = $eventData->event_end_time ?? '05:00 PM';

            $dateText = $startDate->format('M d, Y');
            $timeText = $this->formatTime($startTime) . '-' . $this->formatTime($endTime);

            $eventDates[] = (object) [
                'formatted_date' => $dateText . ' | ' . $timeText,
                'location' => $eventData->event_location ?? 'Location TBA',
                'location_url' => null
            ];
        }

        // Get speakers/moderators
        $speakers = $this->formatSpeakers($eventData->moderator_speaker);

        // Get documents for "Entire Event"
        $entireEventDocuments = $eventData->documents->where('selected_session', 'Entire Event');

        return (object) [
            'id' => $eventData->id,
            'title' => $eventData->event_title,
            'event_dates' => $eventDates,
            'description' => $eventData->event_description ?? 'Event description will be updated soon.',
            'image' => $eventData->event_banner ? asset(env('STORAGE_PATH') . '/event_banners/' . $eventData->event_banner) : asset('assets/bd/images/event.png'),
            'is_registered' => $isRegistered,
            'speakers' => $speakers,
            'expectations' => [
                'Join industry experts to explore innovative healthcare solutions.',
                'Learn about cutting-edge medical technologies and best practices.',
                'Network with healthcare professionals from across the region.',
                'Discover new opportunities for professional development.'
            ],
            'agenda' => $this->generateAgenda($eventData),
            'contact_email' => $eventData->contact_email,
            'entire_event_documents' => $entireEventDocuments
        ];
    }

    /**
     * Build location string from event date data
     */
    private function buildLocationString($eventDate)
    {
        $location = '';

        // Handle location - if it's numeric, use mapping, otherwise use as-is
        if (is_numeric($eventDate->event_location)) {
            $location = $this->getLocationName($eventDate->event_location);
        } else {
            $location = $eventDate->event_location ?? 'Location TBA';
        }

        // Add country if available
        if ($eventDate->event_country) {
            $location = $location . ', ' . $eventDate->event_country;
        }

        return $location;
    }

    /**
     * Get location name by ID
     */
    private function getLocationName($locationId)
    {
        $locations = [
            '1' => 'Main Conference Hall',
            '2' => 'Secondary Meeting Room',
            '12' => 'Auditorium Complex',
        ];

        return $locations[$locationId] ?? "Venue #{$locationId}";
    }

    /**
     * Format time to consistent format
     */
    private function formatTime($time)
    {
        try {
            return Carbon::createFromFormat('h:i A', $time)->format('h:iA');
        } catch (\Exception $e) {
            return $time;
        }
    }

    /**
     * Format speakers from event_moderator_speakers table
     */
    private function formatSpeakers($moderatorSpeakers)
    {
        $speakers = [];

        foreach ($moderatorSpeakers as $speaker) {
            // Determine image path based on type and folder structure
            $imageUrl = asset('assets/bd/images/user.png'); // Default image

            if ($speaker->banner) {
                if ($speaker->type == 1) {
                    // Moderator images are in evet_moderator_moderator folder
                    $imageUrl = asset(env('STORAGE_PATH') . '/evet_moderator_speaker/' . $speaker->banner);
                } else {
                    // Speaker images are in evet_moderator_speaker folder
                    $imageUrl = asset(env('STORAGE_PATH') . '/evet_moderator_moderator/' . $speaker->banner);
                }
            }

            $speakers[] = (object) [
                'id' => $speaker->id,
                'name' => $speaker->name ?? 'Speaker Name',
                'title' => $speaker->title ?? 'Professional Title',
                'image' => $imageUrl,
                'type' => $speaker->type, // 1 = moderator, 2 = speaker
                'description' => $speaker->description ?? ''
            ];
        }

        // If no speakers found, add default ones
        if (empty($speakers)) {
            $speakers = [
                (object) [
                    'id' => 1,
                    'name' => 'Dr. Medical Expert',
                    'title' => 'Chief Medical Officer',
                    'image' => asset('assets/bd/images/user.png'),
                    'type' => 2
                ]
            ];
        }

        return $speakers;
    }

    /**
     * Generate agenda from event sessions
     */
    private function generateAgenda($eventData)
    {
        $agenda = [];

        if ($eventData->sessions && $eventData->sessions->count() > 0) {
            // Event session sorted by time
            $eventSessions = $eventData->sessions->sortBy(function ($session) {
                try {
                    $date = \Carbon\Carbon::createFromFormat('d/m/Y', $session->session_dates);
                    [$start, $end] = array_map('trim', explode('-', $session->session_time_duration));
                    $startTime = \Carbon\Carbon::createFromFormat('g:i A', $start);
                    $endTime = \Carbon\Carbon::createFromFormat('g:i A', $end);
                    return [$date->timestamp, $startTime->timestamp, $endTime->timestamp];
                } catch (\Exception $e) {
                    // Fallback to current timestamp if parsing fails
                    return [now()->timestamp, 0, 0];
                }
            });

            foreach ($eventSessions as $session) {
                $agenda[] = (object) [
                    'time' => $session->session_time_duration ?? 'TBA',
                    'topic' => $session->session_title ?? 'Session Topic',
                    'speaker' => $session->session_speakers ?? 'Speaker TBA',
                    'moderator' => $session->session_moderators ?? 'Moderator TBA'
                ];
            }
        }

        return $agenda;
    }

    /**
     * Send contact message to event organizer
     */
    public function sendContactMessage(Request $request)
    {
        try {
            $user = Session::get('bd_user');

            if (!$user) {
                return response()->json(['success' => false, 'message' => 'Unauthorized'], 401);
            }

            $request->validate([
                'event_id' => 'required|exists:events,id',
                'sender_name' => 'required|string|max:255',
                'sender_email' => 'required|email|max:255',
                'subject' => 'required|string|max:255',
                'message' => 'required|string|max:2000'
            ]);

            // Get event and contact email
            $event = Event::find($request->event_id);

            if (!$event || !$event->contact_email) {
                return response()->json([
                    'success' => false,
                    'message' => 'Event contact email not found'
                ], 404);
            }

            // Prepare email data
            $emailData = [
                'event_title' => $event->event_title,
                'sender_name' => $request->sender_name,
                'sender_email' => $request->sender_email,
                'subject' => $request->subject,
                'message_content' => $request->message,
                'event_id' => $event->id
            ];

            // Send email to event contact
            Mail::send('emails.event-contact', $emailData, function ($message) use ($event, $request) {
                $message->to($event->contact_email)
                        ->subject('Event Inquiry: ' . $request->subject)
                        ->replyTo($request->sender_email, $request->sender_name);
            });

            return response()->json([
                'success' => true,
                'message' => 'Message sent successfully to event organizer'
            ]);

        } catch (\Exception $e) {
            \Log::error('Event contact message error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to send message: ' . $e->getMessage()
            ], 500);
        }
    }
}
